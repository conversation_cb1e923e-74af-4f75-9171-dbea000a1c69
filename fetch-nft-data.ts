import { createPublicClient, http } from 'viem'
import { base } from 'viem/chains'
import { abi } from './abi'
import { readFileSync, writeFileSync } from 'fs'

const CONTRACT_ADDRESS = '0xCbA7f8c936dcCF6E83ff4d86d8fA7Eb56DDb18aa' as const

// Create public client
const publicClient = createPublicClient({
  chain: base,
  transport: http("https://base-mainnet.g.alchemy.com/v2/********************************"),
  batch: {
    multicall: true,
  },
})

interface UserData {
  address: string
  allocation: number
}

interface NFTData extends UserData {
  balanceOf: number
  tokenIds: number[]
  usageData: Array<{
    tokenId: bigint
    max: bigint
    current: bigint
  }>
  totalUsageMax: bigint
  totalUsageCurrent: bigint
}

async function readCSVData(): Promise<UserData[]> {
  const csvContent = readFileSync('/home/<USER>/Documents/augment-projects/vest-subs/data.csv', 'utf-8')
  const lines = csvContent.trim().split('\n')
  const headers = lines[0].split(',')
  
  return lines.slice(1).map(line => {
    const values = line.split(',')
    return {
      address: values[0],
      allocation: parseFloat(values[1])
    }
  })
}

async function fetchNFTData(address: string): Promise<Omit<NFTData, 'address' | 'allocation'>> {
  console.log(`Fetching data for address: ${address}`)
  
  try {
    // Step 1: Get balance of NFTs
    const balanceOf = await publicClient.readContract({
      address: CONTRACT_ADDRESS,
      abi,
      functionName: 'balanceOf',
      args: [address as `0x${string}`],
    }) as bigint

    const balance = Number(balanceOf)
    console.log(`  Balance: ${balance}`)

    if (balance === 0) {
      return {
        balanceOf: 0,
        tokenIds: [],
        usageData: [],
        totalUsageMax: 0n,
        totalUsageCurrent: 0n,
      }
    }

    // Step 2: Get all token IDs for this address
    const tokenIds: bigint[] = []
    for (let i = 0; i < balance; i++) {
      const tokenId = await publicClient.readContract({
        address: CONTRACT_ADDRESS,
        abi,
        functionName: 'tokenOfOwnerByIndex',
        args: [address as `0x${string}`, BigInt(i)],
      }) as bigint
      
      tokenIds.push(tokenId)
    }
    console.log(`  Token IDs: ${tokenIds.join(', ')}`)

    // Step 3: Get usage data for each token
    const usageData = []
    let totalUsageMax = 0n
    let totalUsageCurrent = 0n

    for (const tokenId of tokenIds) {
      const usage = await publicClient.readContract({
        address: CONTRACT_ADDRESS,
        abi,
        functionName: 'getUsage',
        args: [tokenId],
      }) as { max: bigint; current: bigint }

      usageData.push({
        tokenId,
        max: usage.max,
        current: usage.current,
      })

      totalUsageMax += usage.max
      totalUsageCurrent += usage.current
    }

    console.log(`  Total Usage: ${totalUsageCurrent}/${totalUsageMax}`)

    return {
      balanceOf: balance,
      tokenIds,
      usageData,
      totalUsageMax,
      totalUsageCurrent,
    }
  } catch (error) {
    console.error(`Error fetching data for ${address}:`, error)
    return {
      balanceOf: 0,
      tokenIds: [],
      usageData: [],
      totalUsageMax: 0n,
      totalUsageCurrent: 0n,
    }
  }
}

async function main() {
  console.log('Reading CSV data...')
  const userData = await readCSVData()
  console.log(`Found ${userData.length} addresses`)

  const results: NFTData[] = []

  // Process addresses in batches to avoid rate limiting
  const batchSize = 1
  for (let i = 0; i < userData.length; i += batchSize) {
    const batch = userData.slice(i, i + batchSize)
    console.log(`\nProcessing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(userData.length / batchSize)}`)

    const batchPromises = batch.map(async (user) => {
      const nftData = await fetchNFTData(user.address)
      return {
        ...user,
        ...nftData,
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)

    // Add a small delay between batches
    if (i + batchSize < userData.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  // Convert results to CSV format
  const csvHeaders = [
    'address',
    'allocation',
    'balanceOf',
    'tokenIds',
    'totalUsageMax',
    'totalUsageCurrent',
    'usageDetails'
  ]

  const csvRows = results.map(result => [
    result.address,
    result.allocation,
    result.balanceOf,
    result.tokenIds.join(';'),
    result.totalUsageMax.toString(),
    result.totalUsageCurrent.toString(),
    result.usageData.map(u => `${u.tokenId}:${u.current}/${u.max}`).join(';')
  ])

  const csvContent = [
    csvHeaders.join(','),
    ...csvRows.map(row => row.join(','))
  ].join('\n')

  // Write results to file
  const outputFile = 'nft-usage-results.csv'
  writeFileSync(outputFile, csvContent)
  console.log(`\nResults written to ${outputFile}`)

  // Print summary
  const totalAddresses = results.length
  const addressesWithNFTs = results.filter(r => r.balanceOf > 0).length
  const totalNFTs = results.reduce((sum, r) => sum + r.balanceOf, 0)
  const totalUsageSum = results.reduce((sum, r) => sum + Number(r.totalUsageCurrent), 0)

  console.log('\n=== SUMMARY ===')
  console.log(`Total addresses processed: ${totalAddresses}`)
  console.log(`Addresses with NFTs: ${addressesWithNFTs}`)
  console.log(`Total NFTs found: ${totalNFTs}`)
  console.log(`Total usage sum: ${totalUsageSum}`)
}

// Run the script
main().catch(console.error)